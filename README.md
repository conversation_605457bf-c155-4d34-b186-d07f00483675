# Node Express API with PostgreSQL

A RESTful API built with Express.js and PostgreSQL for task management.

## Features

- ✅ **PostgreSQL Database**: Full PostgreSQL integration with connection
  pooling
- ✅ **Auto Database Setup**: Automatically creates database and tables on
  startup
- ✅ **Task Management**: Complete CRUD operations for tasks
- ✅ **Raw SQL**: Pure SQL implementation without ORM
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Environment Configuration**: Configurable via environment variables

## Database Schema

### Tasks Table

```sql
CREATE TABLE tasks (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Environment Variables

Copy `.env.example` to `.env` and configure:

```env
# Application Configuration
APP_PORT=3000
APP_DEBUG=true
API_VERSION=v1
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_NAME=tasks_db
```

## Getting Started

1. **Install Dependencies**

   ```bash
   npm install
   ```

2. **Setup Environment**

   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

The application will automatically:

- Connect to PostgreSQL
- Create the database if it doesn't exist
- Create the tasks table and triggers
- Start the server on http://localhost:3000

## API Routes

- **GET** `/` - API information
- **GET** `/api/v1/tasks` - Get all tasks
- **POST** `/api/v1/tasks` - Create a new task
- **GET** `/api/v1/tasks/:taskID` - Get a specific task
- **PUT** `/api/v1/tasks/:taskID` - Update a specific task
- **DELETE** `/api/v1/tasks/:taskID` - Delete a specific task

## Database Functions

The database module exports the following functions:

```javascript
import {
  initializeDatabase,
  query,
  getPool,
  closeDatabase
} from './src/config/database.js'

// Initialize database (called automatically on app start)
await initializeDatabase()

// Execute queries
const result = await query('SELECT * FROM tasks WHERE id = $1', [taskId])

// Get connection pool
const pool = getPool()

// Close database connection
await closeDatabase()
```
